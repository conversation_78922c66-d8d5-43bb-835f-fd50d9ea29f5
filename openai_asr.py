###############################################################################
#  Copyright (C) 2024 LiveTalking@lipku https://github.com/lipku/LiveTalking
#  email: <EMAIL>
# 
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#  
#       http://www.apache.org/licenses/LICENSE-2.0
# 
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
###############################################################################

import time
import numpy as np
import os
import io
import tempfile
import queue
from queue import Queue
import torch.multiprocessing as mp
from baseasr import BaseASR
from openai import OpenAI
import soundfile as sf
from pydub import AudioSegment
from logger import logger

class OpenAIASR(BaseASR):
    def __init__(self, opt, parent, audio_processor=None):
        super().__init__(opt, parent)
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key=os.getenv("OPENAI_API_KEY"),
            # 如果使用其他兼容的API服务，可以设置base_url
            # base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        )
        
        # ASR配置
        self.model = "whisper-1"  # OpenAI Whisper API模型
        self.language = opt.get('asr_language', 'zh')  # 默认中文，可配置
        self.response_format = "text"  # 可选: text, json, srt, verbose_json, vtt
        
        # 音频缓冲配置
        self.audio_buffer = []
        self.buffer_duration = 3.0  # 3秒缓冲，可配置
        self.min_audio_length = 1.0  # 最小音频长度1秒
        self.last_transcribe_time = 0
        self.transcribe_interval = 0.5  # 最小转录间隔0.5秒
        
        # 音频质量检测
        self.silence_threshold = 0.01  # 静音阈值
        self.min_speech_duration = 0.3  # 最小语音持续时间
        
        logger.info(f"OpenAI ASR initialized with model: {self.model}, language: {self.language}")

    def _is_speech(self, audio_data):
        """检测音频是否包含语音"""
        if len(audio_data) == 0:
            return False
        
        # 计算音频能量
        energy = np.mean(np.abs(audio_data))
        return energy > self.silence_threshold

    def _prepare_audio_for_api(self, audio_data):
        """准备音频数据用于API调用"""
        try:
            # 确保音频数据是float32格式
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                # 使用soundfile写入WAV文件
                sf.write(temp_file.name, audio_data, self.sample_rate)
                return temp_file.name
                
        except Exception as e:
            logger.error(f"Error preparing audio for API: {e}")
            return None

    def _transcribe_audio(self, audio_file_path):
        """调用OpenAI Whisper API进行转录"""
        try:
            with open(audio_file_path, 'rb') as audio_file:
                transcript = self.client.audio.transcriptions.create(
                    model=self.model,
                    file=audio_file,
                    language=self.language,
                    response_format=self.response_format,
                    temperature=0.0  # 使用确定性输出
                )
            
            # 清理临时文件
            os.unlink(audio_file_path)
            
            if self.response_format == "text":
                return transcript.strip()
            else:
                return transcript.text.strip() if hasattr(transcript, 'text') else str(transcript).strip()
                
        except Exception as e:
            logger.error(f"Error calling OpenAI Whisper API: {e}")
            # 清理临时文件
            if os.path.exists(audio_file_path):
                os.unlink(audio_file_path)
            return ""

    def run_step(self):
        """ASR处理步骤"""
        start_time = time.time()
        
        # 收集音频帧
        for _ in range(self.batch_size * 2):
            audio_frame, type, eventpoint = self.get_audio_frame()
            self.frames.append(audio_frame)
            self.output_queue.put((audio_frame, type, eventpoint))
            
            # 如果是语音帧，添加到缓冲区
            if type == 0 and self._is_speech(audio_frame):
                self.audio_buffer.extend(audio_frame)
        
        if len(self.frames) <= self.stride_left_size + self.stride_right_size:
            return
        
        # 检查是否需要进行转录
        current_time = time.time()
        buffer_duration = len(self.audio_buffer) / self.sample_rate
        
        should_transcribe = (
            buffer_duration >= self.buffer_duration or
            (buffer_duration >= self.min_audio_length and 
             current_time - self.last_transcribe_time >= self.transcribe_interval)
        )
        
        if should_transcribe and len(self.audio_buffer) > 0:
            # 转换为numpy数组
            audio_data = np.array(self.audio_buffer, dtype=np.float32)
            
            # 检查音频长度
            if len(audio_data) / self.sample_rate >= self.min_speech_duration:
                # 准备音频文件
                audio_file_path = self._prepare_audio_for_api(audio_data)
                
                if audio_file_path:
                    # 调用API进行转录
                    transcript = self._transcribe_audio(audio_file_path)
                    
                    if transcript:
                        logger.info(f"OpenAI ASR result: {transcript}")
                        # 调用父类的ASR结果处理方法
                        if self.parent and hasattr(self.parent, 'on_asr_result'):
                            self.parent.on_asr_result(transcript)
                        elif self.parent and hasattr(self.parent, 'put_msg_txt'):
                            # 兼容现有的消息处理接口
                            self.parent.put_msg_txt(transcript)
                    
                    self.last_transcribe_time = current_time
            
            # 清空缓冲区
            self.audio_buffer = []
        
        # 保持滑动窗口
        self.frames = self.frames[-(self.stride_left_size + self.stride_right_size):]
        
        # 为了兼容现有接口，生成空的特征
        # 实际的转录结果通过回调或事件处理
        dummy_features = [np.zeros((1, 384)) for _ in range(self.batch_size)]
        self.feat_queue.put(dummy_features)
        
        processing_time = (time.time() - start_time) * 1000
        logger.debug(f"OpenAI ASR processing time: {processing_time:.2f}ms")

    def get_transcription_result(self):
        """获取转录结果（如果需要同步接口）"""
        # 这个方法可以用于同步获取转录结果
        # 具体实现取决于你的需求
        pass

    def set_language(self, language):
        """设置识别语言"""
        self.language = language
        logger.info(f"OpenAI ASR language set to: {language}")

    def set_model(self, model):
        """设置模型"""
        self.model = model
        logger.info(f"OpenAI ASR model set to: {model}")
