#!/bin/bash

# LiveTalking with OpenAI ASR 启动脚本
# 使用OpenAI Whisper API进行语音转文字

echo "🚀 启动 LiveTalking with OpenAI ASR"
echo "=================================="

# 检查环境变量
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ 错误: 未设置 OPENAI_API_KEY 环境变量"
    echo "请设置您的OpenAI API密钥:"
    echo "export OPENAI_API_KEY='your-openai-api-key-here'"
    exit 1
fi

echo "✅ 检测到 OpenAI API 密钥"

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python"
    exit 1
fi

# 检查依赖
echo "🔍 检查依赖..."
python -c "import openai, pydub, soundfile" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少依赖，正在安装..."
    pip install openai>=1.0.0 pydub soundfile
fi

# 设置默认参数
MODEL=${MODEL:-"musetalk"}
ASR_LANGUAGE=${ASR_LANGUAGE:-"zh"}
AVATAR_ID=${AVATAR_ID:-"avator_1"}
PORT=${PORT:-8010}

echo "📋 启动参数:"
echo "   模型: $MODEL"
echo "   ASR语言: $ASR_LANGUAGE"
echo "   头像: $AVATAR_ID"
echo "   端口: $PORT"
echo ""

# 启动应用
echo "🎬 启动应用..."
python app.py \
    --model "$MODEL" \
    --asr openai \
    --asr_language "$ASR_LANGUAGE" \
    --avatar_id "$AVATAR_ID" \
    --listenport "$PORT" \
    "$@"

echo "👋 应用已退出"
