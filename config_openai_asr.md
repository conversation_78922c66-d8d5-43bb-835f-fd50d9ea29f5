# OpenAI ASR 配置说明

## 环境变量配置

在使用OpenAI ASR之前，需要设置以下环境变量：

```bash
# 设置OpenAI API密钥
export OPENAI_API_KEY="your-openai-api-key-here"

# 可选：如果使用其他兼容的API服务
# export OPENAI_BASE_URL="https://your-custom-api-endpoint.com/v1"
```

## 启动参数

使用OpenAI ASR启动应用：

```bash
# 使用OpenAI ASR，默认中文识别
python app.py --asr openai --asr_language zh

# 使用OpenAI ASR，英文识别
python app.py --asr openai --asr_language en

# 使用OpenAI ASR，自动检测语言（不指定语言）
python app.py --asr openai

# 结合其他参数使用
python app.py --model musetalk --asr openai --asr_language zh --avatar_id avator_1
```

## ASR类型对比

| ASR类型 | 描述 | 优点 | 缺点 |
|---------|------|------|------|
| hubert | 本地Hubert模型 | 快速，无网络依赖 | 准确率相对较低 |
| muse | 本地Whisper模型 | 较好的准确率，无网络依赖 | 计算资源消耗较大 |
| openai | OpenAI Whisper API | 最高准确率，支持多语言 | 需要网络，有API调用成本 |

## 支持的语言

OpenAI Whisper API支持多种语言，常用的包括：

- `zh` - 中文
- `en` - 英文
- `ja` - 日文
- `ko` - 韩文
- `es` - 西班牙文
- `fr` - 法文
- `de` - 德文
- `it` - 意大利文
- `pt` - 葡萄牙文
- `ru` - 俄文

## 配置参数

在`openai_asr.py`中可以调整以下参数：

```python
# 音频缓冲配置
self.buffer_duration = 3.0  # 3秒缓冲，可调整
self.min_audio_length = 1.0  # 最小音频长度1秒
self.transcribe_interval = 0.5  # 最小转录间隔0.5秒

# 音频质量检测
self.silence_threshold = 0.01  # 静音阈值
self.min_speech_duration = 0.3  # 最小语音持续时间
```

## 注意事项

1. **API密钥安全**：请妥善保管您的OpenAI API密钥，不要在代码中硬编码
2. **网络连接**：确保服务器能够访问OpenAI API
3. **成本控制**：OpenAI API按使用量计费，请注意控制调用频率
4. **延迟考虑**：网络API调用会有一定延迟，适合对实时性要求不是特别严格的场景
5. **错误处理**：代码已包含基本的错误处理，但建议根据实际需求进行调整

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error calling OpenAI Whisper API: Incorrect API key provided
   ```
   解决：检查OPENAI_API_KEY环境变量是否正确设置

2. **网络连接问题**
   ```
   Error calling OpenAI Whisper API: Connection error
   ```
   解决：检查网络连接，确保能访问OpenAI API

3. **音频格式问题**
   ```
   Error preparing audio for API: ...
   ```
   解决：检查音频数据格式，确保soundfile库正确安装

### 调试模式

启用详细日志：

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 性能优化建议

1. **调整缓冲区大小**：根据实际需求调整`buffer_duration`
2. **优化转录间隔**：调整`transcribe_interval`以平衡实时性和API调用频率
3. **语音检测优化**：调整`silence_threshold`以提高语音检测准确性
4. **批量处理**：对于非实时场景，可以考虑批量处理音频以减少API调用次数
