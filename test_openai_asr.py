#!/usr/bin/env python3
"""
OpenAI ASR 测试脚本
用于测试OpenAI Whisper API的语音转文字功能
"""

import os
import sys
import time
import numpy as np
import soundfile as sf
from openai import OpenAI
import tempfile
from logger import logger

def test_openai_api():
    """测试OpenAI API连接"""
    try:
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        # 创建一个简单的测试音频（1秒的正弦波）
        sample_rate = 16000
        duration = 1.0
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = 0.3 * np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        # 保存为临时文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            sf.write(temp_file.name, audio_data, sample_rate)
            temp_path = temp_file.name
        
        try:
            # 调用OpenAI API
            with open(temp_path, 'rb') as audio_file:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language="zh",
                    response_format="text"
                )
            
            print(f"✅ OpenAI API连接成功")
            print(f"转录结果: {transcript}")
            return True
            
        finally:
            # 清理临时文件
            os.unlink(temp_path)
            
    except Exception as e:
        print(f"❌ OpenAI API连接失败: {e}")
        return False

def test_openai_asr_class():
    """测试OpenAI ASR类"""
    try:
        # 模拟opt参数
        class MockOpt:
            def __init__(self):
                self.fps = 50
                self.l = 10
                self.r = 10
                self.batch_size = 16
                self.asr_language = 'zh'
            
            def get(self, key, default=None):
                return getattr(self, key, default)
        
        opt = MockOpt()
        
        # 导入并测试OpenAI ASR类
        from openai_asr import OpenAIASR
        
        asr = OpenAIASR(opt, None)
        print(f"✅ OpenAI ASR类初始化成功")
        print(f"模型: {asr.model}")
        print(f"语言: {asr.language}")
        print(f"采样率: {asr.sample_rate}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI ASR类测试失败: {e}")
        return False

def test_audio_processing():
    """测试音频处理功能"""
    try:
        from openai_asr import OpenAIASR
        
        class MockOpt:
            def __init__(self):
                self.fps = 50
                self.l = 10
                self.r = 10
                self.batch_size = 16
                self.asr_language = 'zh'
            
            def get(self, key, default=None):
                return getattr(self, key, default)
        
        opt = MockOpt()
        asr = OpenAIASR(opt, None)
        
        # 创建测试音频数据
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = 0.3 * np.sin(2 * np.pi * 440 * t).astype(np.float32)
        
        # 测试语音检测
        is_speech = asr._is_speech(audio_data)
        print(f"语音检测结果: {is_speech}")
        
        # 测试音频准备
        audio_file_path = asr._prepare_audio_for_api(audio_data)
        if audio_file_path:
            print(f"✅ 音频文件准备成功: {audio_file_path}")
            # 清理文件
            os.unlink(audio_file_path)
        else:
            print(f"❌ 音频文件准备失败")
            return False
        
        print(f"✅ 音频处理功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 音频处理功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试OpenAI ASR功能...")
    print("=" * 50)
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 未设置OPENAI_API_KEY环境变量")
        print("请设置环境变量: export OPENAI_API_KEY='your-api-key'")
        return False
    
    print(f"✅ 检测到API密钥: {api_key[:10]}...")
    print()
    
    # 测试项目
    tests = [
        ("OpenAI API连接测试", test_openai_api),
        ("OpenAI ASR类测试", test_openai_asr_class),
        ("音频处理功能测试", test_audio_processing),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 {test_name}...")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append(False)
        print()
    
    # 总结
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OpenAI ASR功能正常")
        print("\n使用方法:")
        print("python app.py --asr openai --asr_language zh")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
