# LiveTalking OpenAI ASR 集成指南

## 概述

本项目已集成OpenAI Whisper API作为语音转文字(ASR)的选项，提供更高精度的语音识别能力。

## 当前ASR架构分析

### 原有ASR方案

1. **HubertASR** (`hubertasr.py`) - 基于Hubert模型的本地ASR
2. **MuseASR** (`museasr.py`) - 基于本地Whisper模型的ASR
3. **Web ASR** (`web/asr/`) - 基于WebSocket的在线ASR服务

### 新增OpenAI ASR

- **OpenAIASR** (`openai_asr.py`) - 基于OpenAI Whisper API的云端ASR
- 支持多语言识别
- 更高的识别准确率
- 实时音频缓冲和处理

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install openai>=1.0.0 pydub soundfile

# 设置API密钥
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 2. 启动应用

```bash
# 使用OpenAI ASR，中文识别
python app.py --asr openai --asr_language zh

# 使用OpenAI ASR，英文识别  
python app.py --asr openai --asr_language en

# 结合其他参数
python app.py --model musetalk --asr openai --asr_language zh --avatar_id avator_1
```

### 3. 使用启动脚本

```bash
# 给脚本执行权限
chmod +x start_with_openai_asr.sh

# 启动
./start_with_openai_asr.sh
```

## 配置选项

### 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--asr` | `hubert` | ASR类型: `hubert`, `muse`, `openai` |
| `--asr_language` | `zh` | OpenAI ASR识别语言 |

### 支持的语言

- `zh` - 中文
- `en` - 英文  
- `ja` - 日文
- `ko` - 韩文
- `es` - 西班牙文
- `fr` - 法文
- `de` - 德文
- 更多语言请参考OpenAI文档

## ASR方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **HubertASR** | 快速、本地处理、无网络依赖 | 准确率相对较低 | 对延迟要求极高的场景 |
| **MuseASR** | 较好准确率、本地处理 | 计算资源消耗大 | 有GPU资源且要求本地处理 |
| **OpenAIASR** | 最高准确率、多语言支持 | 需要网络、有API成本 | 对准确率要求高的场景 |

## 技术实现

### 核心特性

1. **实时音频缓冲**: 收集音频数据到缓冲区
2. **智能语音检测**: 过滤静音片段
3. **自适应转录**: 根据音频长度和时间间隔触发转录
4. **错误处理**: 完善的异常处理和重试机制
5. **兼容性**: 与现有架构完全兼容

### 音频处理流程

```
音频输入 → 语音检测 → 缓冲区累积 → 触发条件检查 → API调用 → 结果处理
```

### 关键参数

```python
# 在openai_asr.py中可调整
buffer_duration = 3.0      # 缓冲区时长(秒)
min_audio_length = 1.0     # 最小音频长度(秒)  
transcribe_interval = 0.5  # 转录间隔(秒)
silence_threshold = 0.01   # 静音阈值
```

## 测试

### 运行测试脚本

```bash
# 测试OpenAI ASR功能
python test_openai_asr.py
```

测试内容包括:
- OpenAI API连接测试
- OpenAI ASR类初始化测试
- 音频处理功能测试

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: Incorrect API key provided
   ```
   解决: 检查`OPENAI_API_KEY`环境变量

2. **网络连接问题**
   ```
   Error: Connection error
   ```
   解决: 检查网络连接和防火墙设置

3. **依赖缺失**
   ```
   ModuleNotFoundError: No module named 'openai'
   ```
   解决: `pip install openai>=1.0.0 pydub soundfile`

### 调试模式

启用详细日志:
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 性能优化

1. **调整缓冲区大小**: 根据实时性需求调整`buffer_duration`
2. **优化转录频率**: 调整`transcribe_interval`平衡实时性和成本
3. **语音检测优化**: 调整`silence_threshold`提高检测准确性

## 成本考虑

- OpenAI Whisper API按音频时长计费
- 建议监控API使用量
- 可通过调整参数控制调用频率

## 扩展功能

### 自定义API端点

如果使用兼容的API服务:

```bash
export OPENAI_BASE_URL="https://your-api-endpoint.com/v1"
```

### 添加回调处理

在`openai_asr.py`中可以扩展结果处理逻辑:

```python
def on_transcription_result(self, transcript):
    # 自定义处理逻辑
    pass
```

## 贡献

欢迎提交Issue和Pull Request来改进OpenAI ASR集成。

## 许可证

遵循项目原有的Apache 2.0许可证。
